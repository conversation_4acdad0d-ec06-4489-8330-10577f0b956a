import { toast } from "@/core/toast";
import { useAtom, useAtomValue } from "jotai";
import { useCallback, useEffect, useRef } from "react";
import {
	addMessageAtom,
	chatStateAtom,
	createSessionAtom,
	openChatAtom,
	sessionIdAtom,
	setErrorAtom,
	setLoadingStateAtom,
	setStreamingState<PERSON>tom,
	toggleChatAtom,
	updateMessageAtom,
} from "../atoms";
import { chatStreamService } from "../services";
import type { IChatMessage, IChatStreamRequest } from "../types";

export const useChat = () => {
	const chatState = useAtomValue(chatStateAtom);
	const sessionId = useAtomValue(sessionIdAtom);
	
	const [, addMessage] = useAtom(addMessageAtom);
	const [, updateMessage] = useAtom(updateMessageAtom);
	const [, createSession] = useAtom(createSessionAtom);
	const [, openChat] = useAtom(openChatAtom);
	const [, toggleChat] = useAtom(toggleChatAtom);
	const [, setStreaming] = useAtom(setStreamingStateAtom);
	const [, setLoading] = useAtom(setLoadingStateAtom);
	const [, setError] = useAtom(setErrorAtom);

	const streamingMessageRef = useRef<string | null>(null);
	const accumulatedContentRef = useRef<string>("");

	// Ensure we have a session when chat is opened
	useEffect(() => {
		if (chatState.isOpen && !sessionId) {
			createSession();
		}
	}, [chatState.isOpen, sessionId, createSession]);

	const sendMessage = useCallback(async (content: string) => {
		if (!content.trim()) return;

		// Clear any previous errors
		setError(null);

		// Ensure we have a session
		let currentSessionId = sessionId;
		if (!currentSessionId) {
			currentSessionId = createSession();
		}

		if (!currentSessionId) {
			setError("Erro ao criar sessão de chat");
			return;
		}

		try {
			// Add user message
			const userMessageId = addMessage({
				content: content.trim(),
				role: "user",
			});

			// Create assistant message placeholder
			const assistantMessageId = addMessage({
				content: "",
				role: "assistant",
				isStreaming: true,
			});

			if (!assistantMessageId) {
				throw new Error("Erro ao criar mensagem do assistente");
			}

			// Set streaming state
			setStreaming(true, assistantMessageId);
			streamingMessageRef.current = assistantMessageId;
			accumulatedContentRef.current = "";

			// Prepare request
			const request: IChatStreamRequest = {
				message: content.trim(),
				sessionId: currentSessionId,
				context: {},
				model: "llama2",
				temperature: 1,
			};

			// Start streaming
			await chatStreamService.streamChat(request, {
				onChunk: (chunk: string) => {
					accumulatedContentRef.current += chunk;
					updateMessage(assistantMessageId, {
						content: accumulatedContentRef.current,
						isStreaming: true,
					});
				},
				onComplete: (fullMessage: string) => {
					updateMessage(assistantMessageId, {
						content: fullMessage,
						isStreaming: false,
					});
					setStreaming(false);
					streamingMessageRef.current = null;
					accumulatedContentRef.current = "";
				},
				onError: (error) => {
					updateMessage(assistantMessageId, {
						content: error.message || "Erro ao processar resposta",
						isStreaming: false,
						isError: true,
					});
					setStreaming(false);
					setError(error.message || "Erro ao processar resposta");
					streamingMessageRef.current = null;
					accumulatedContentRef.current = "";
					
					toast.error("Erro no chat: " + error.message);
				},
			});
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : "Erro desconhecido";
			setError(errorMessage);
			setStreaming(false);
			streamingMessageRef.current = null;
			accumulatedContentRef.current = "";
			
			toast.error("Erro ao enviar mensagem: " + errorMessage);
		}
	}, [
		sessionId,
		addMessage,
		updateMessage,
		createSession,
		setStreaming,
		setError,
	]);

	const stopStreaming = useCallback(() => {
		chatStreamService.abortStream();
		
		if (streamingMessageRef.current) {
			updateMessage(streamingMessageRef.current, {
				content: accumulatedContentRef.current || "Resposta interrompida",
				isStreaming: false,
			});
		}
		
		setStreaming(false);
		streamingMessageRef.current = null;
		accumulatedContentRef.current = "";
	}, [updateMessage, setStreaming]);

	const openChatModal = useCallback(() => {
		openChat();
	}, [openChat]);

	const toggleChatModal = useCallback(() => {
		toggleChat();
	}, [toggleChat]);

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			if (chatStreamService.isStreaming()) {
				chatStreamService.abortStream();
			}
		};
	}, []);

	return {
		// State
		chatState,
		sessionId,
		
		// Actions
		sendMessage,
		stopStreaming,
		openChat: openChatModal,
		toggleChat: toggleChatModal,
		
		// Computed
		isStreaming: chatState.isStreaming,
		isOpen: chatState.isOpen,
		hasError: !!chatState.error,
		error: chatState.error,
		messages: chatState.currentSession?.messages || [],
		hasMessages: (chatState.currentSession?.messages.length || 0) > 0,
	};
};
