import { Modal } from "@/shared/components/custom/modal";
import { But<PERSON> } from "@/shared/components/shadcn/button";
import { ScrollArea } from "@/shared/components/shadcn/scroll-area";
import { cn } from "@/shared/lib/shadcn/utils";
import { useAtom, useAtomValue } from "jotai";
import { MessageSquare, Trash2 } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { chatIsOpenAtom, chatIsStreamingAtom, clearSessionAtom, closeChatAtom, hasMessagesAtom, messagesAtom, streamingMessageIdAtom } from "../atoms";
import { ChatInput } from "./chat-input";
import { ChatMessage } from "./chat-message";
import { TypingIndicator } from "./typing-indicator";

interface ChatModalProps {
	onSendMessage: (message: string) => void;
	onStopStreaming?: () => void;
	className?: string;
}

export const ChatModal = ({ onSendMessage, onStopStreaming, className }: ChatModalProps) => {
	const isOpen = useAtomValue(chatIsOpenAtom);
	const [, closeChat] = useAtom(closeChatAtom);
	const [, clearSession] = useAtom(clearSessionAtom);

	const messages = useAtomValue(messagesAtom);
	const isStreaming = useAtomValue(chatIsStreamingAtom);
	const streamingMessageId = useAtomValue(streamingMessageIdAtom);
	const hasMessages = useAtomValue(hasMessagesAtom);

	const [inputValue, setInputValue] = useState("");
	const scrollAreaRef = useRef<HTMLDivElement>(null);
	const inputRef = useRef<HTMLTextAreaElement>(null);

	// Auto-scroll to bottom when new messages arrive
	useEffect(() => {
		if (scrollAreaRef.current) {
			const scrollContainer = scrollAreaRef.current.querySelector("[data-radix-scroll-area-viewport]");
			if (scrollContainer) {
				scrollContainer.scrollTop = scrollContainer.scrollHeight;
			}
		}
	}, [messages, isStreaming]);

	// Focus input when modal opens
	useEffect(() => {
		if (isOpen && inputRef.current) {
			setTimeout(() => inputRef.current?.focus(), 100);
		}
	}, [isOpen]);

	const handleSend = () => {
		if (inputValue.trim()) {
			onSendMessage(inputValue.trim());
			setInputValue("");
		}
	};

	const handleClearChat = () => {
		clearSession();
	};

	const handleClose = () => {
		closeChat();
	};

	return (
		<Modal isOpen={isOpen} onClose={handleClose} title="Chat IA" size="lg" className={cn("flex !h-[600px] !max-w-2xl flex-col", className)}>
			<div className="flex h-full flex-col">
				{/* Header with actions */}
				{hasMessages && (
					<div className="flex justify-end border-b p-2">
						<Button onClick={handleClearChat} variant="ghost" size="sm" className="text-muted-foreground hover:text-destructive">
							<Trash2 className="mr-2 h-4 w-4" />
							Limpar conversa
						</Button>
					</div>
				)}

				{/* Messages area */}
				<ScrollArea ref={scrollAreaRef} className="h-0 flex-1">
					{!hasMessages ? (
						<div className="flex h-full flex-col items-center justify-center p-8 text-center">
							<MessageSquare className="text-muted-foreground/50 mb-4 h-12 w-12" />
							<h3 className="text-muted-foreground mb-2 text-lg font-medium">Bem-vindo ao Chat IA</h3>
							<p className="text-muted-foreground max-w-sm text-sm">
								Faça perguntas, peça ajuda ou converse sobre qualquer assunto. Estou aqui para ajudar!
							</p>
						</div>
					) : (
						<div className="space-y-1">
							{messages.map(message => (
								<ChatMessage key={message.id} message={message} isStreaming={isStreaming && message.id === streamingMessageId} />
							))}
							{isStreaming && !streamingMessageId && <TypingIndicator />}
						</div>
					)}
				</ScrollArea>

				{/* Input area */}
				<ChatInput
					ref={inputRef}
					value={inputValue}
					onChange={setInputValue}
					onSend={handleSend}
					onStop={onStopStreaming}
					isStreaming={isStreaming}
					placeholder="Digite sua mensagem... (Enter para enviar, Shift+Enter para nova linha)"
				/>
			</div>
		</Modal>
	);
};
