export interface IChatMessage {
	id: string;
	content: string;
	role: "user" | "assistant";
	timestamp: Date;
	isStreaming?: boolean;
	isError?: boolean;
}

export interface IChatSession {
	id: string;
	messages: IChatMessage[];
	createdAt: Date;
	updatedAt: Date;
}

export interface IChatStreamRequest {
	message: string;
	sessionId: string;
	context?: Record<string, unknown>;
	model?: string;
	temperature?: number;
}

export interface IChatStreamResponse {
	content: string;
	isComplete: boolean;
	sessionId: string;
	messageId: string;
}

export interface IChatState {
	currentSession: IChatSession | null;
	isOpen: boolean;
	isStreaming: boolean;
	isLoading: boolean;
	error: string | null;
	streamingMessageId: string | null;
}

export interface IChatStreamChunk {
	data: string;
	done: boolean;
}

export type ChatMessageRole = "user" | "assistant";

export interface IChatError {
	message: string;
	code?: string;
	details?: unknown;
}

export interface IChatStreamOptions {
	onChunk?: (chunk: string) => void;
	onComplete?: (fullMessage: string) => void;
	onError?: (error: IChatError) => void;
	signal?: AbortSignal;
}
