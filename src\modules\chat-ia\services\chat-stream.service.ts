import { axiosInstance } from "@/config/api/instance";
import { CHAT_IA_ENDPOINTS } from "../api/endpoints";
import type { IChatStreamRequest, IChatStreamOptions, IChatError } from "../types";

export class ChatStreamService {
	private abortController: AbortController | null = null;

	async streamChat(request: IChatStreamRequest, options: IChatStreamOptions = {}): Promise<void> {
		// Abort any existing stream
		this.abortStream();

		// Create new abort controller
		this.abortController = new AbortController();
		const signal = options.signal || this.abortController.signal;

		try {
			const response = await fetch(`${axiosInstance.defaults.baseURL}${CHAT_IA_ENDPOINTS.TO_TALK_STREAM}`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					// Include any auth headers from axios instance
					...this.getAuthHeaders(),
				},
				body: JSON.stringify({
					message: request.message,
					sessionId: request.sessionId,
					context: request.context || {},
					model: request.model || "llama2",
					temperature: request.temperature || 1,
				}),
				signal,
				credentials: "include", // Include cookies for auth
			});

			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`);
			}

			if (!response.body) {
				throw new Error("No response body received");
			}

			const reader = response.body.getReader();
			const decoder = new TextDecoder();
			let fullMessage = "";

			try {
				while (true) {
					const { done, value } = await reader.read();

					if (done) {
						break;
					}

					const chunk = decoder.decode(value, { stream: true });
					const lines = chunk.split("\n");

					for (const line of lines) {
						if (line.trim() === "") continue;

						// Handle Server-Sent Events format
						if (line.startsWith("data: ")) {
							const data = line.slice(6);
							
							if (data === "[DONE]") {
								options.onComplete?.(fullMessage);
								return;
							}

							try {
								const parsed = JSON.parse(data);
								const content = parsed.content || parsed.delta?.content || "";
								
								if (content) {
									fullMessage += content;
									options.onChunk?.(content);
								}
							} catch (parseError) {
								// If not JSON, treat as plain text
								fullMessage += data;
								options.onChunk?.(data);
							}
						} else {
							// Handle plain text streaming
							fullMessage += line;
							options.onChunk?.(line);
						}
					}
				}

				options.onComplete?.(fullMessage);
			} finally {
				reader.releaseLock();
			}
		} catch (error) {
			if (signal.aborted) {
				// Stream was intentionally aborted
				return;
			}

			const chatError: IChatError = {
				message: error instanceof Error ? error.message : "Unknown error occurred",
				details: error,
			};

			options.onError?.(chatError);
			throw chatError;
		} finally {
			this.abortController = null;
		}
	}

	abortStream(): void {
		if (this.abortController) {
			this.abortController.abort();
			this.abortController = null;
		}
	}

	private getAuthHeaders(): Record<string, string> {
		// Get auth headers from axios instance interceptors
		// This ensures we include the same auth headers that other requests use
		const headers: Record<string, string> = {};
		
		// Check if we have cookies or auth tokens
		if (typeof window !== "undefined") {
			// Get cookies that might contain auth tokens
			const cookies = document.cookie;
			if (cookies) {
				// The axios interceptors will handle auth, but for fetch we need to manually include
				// For now, we rely on credentials: "include" to send cookies
			}
		}

		return headers;
	}

	isStreaming(): boolean {
		return this.abortController !== null;
	}
}

// Export singleton instance
export const chatStreamService = new ChatStreamService();
