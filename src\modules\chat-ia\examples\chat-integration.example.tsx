/**
 * Example of how to integrate the Chat IA system into your application
 * 
 * This example shows different ways to use the chat components:
 * 1. Complete chat with floating trigger button
 * 2. Chat modal only (controlled externally)
 * 3. Custom trigger button
 */

import { Button } from "@/shared/components/shadcn/button";
import { MessageSquare } from "lucide-react";
import { Chat, ChatModal, ChatTrigger, useChat } from "../index";

// Example 1: Complete chat system with floating trigger
export const ChatExample1 = () => {
	return (
		<div>
			{/* Your app content */}
			<h1>My Application</h1>
			<p>This is my application content...</p>
			
			{/* Chat system - will show floating trigger and modal */}
			<Chat />
		</div>
	);
};

// Example 2: Chat modal only (controlled externally)
export const ChatExample2 = () => {
	const { sendMessage, stopStreaming, openChat, isOpen } = useChat();

	return (
		<div>
			{/* Your app content */}
			<h1>My Application</h1>
			
			{/* Custom trigger button */}
			<Button onClick={openChat} className="mb-4">
				<MessageSquare className="h-4 w-4 mr-2" />
				Open Chat
			</Button>
			
			{/* Chat modal only */}
			<ChatModal 
				onSendMessage={sendMessage}
				onStopStreaming={stopStreaming}
			/>
		</div>
	);
};

// Example 3: Custom positioned trigger
export const ChatExample3 = () => {
	const { sendMessage, stopStreaming } = useChat();

	return (
		<div>
			{/* Your app content */}
			<h1>My Application</h1>
			
			{/* Custom positioned trigger */}
			<ChatTrigger 
				position="bottom-left"
				size="lg"
				className="bg-green-500 hover:bg-green-600"
			/>
			
			{/* Chat modal */}
			<ChatModal 
				onSendMessage={sendMessage}
				onStopStreaming={stopStreaming}
			/>
		</div>
	);
};

// Example 4: Using the hook for custom implementation
export const ChatExample4 = () => {
	const { 
		sendMessage, 
		stopStreaming, 
		isOpen, 
		isStreaming, 
		messages, 
		hasMessages,
		toggleChat,
		error 
	} = useChat();

	return (
		<div>
			<h1>Custom Chat Implementation</h1>
			
			<div className="space-y-4">
				<Button onClick={toggleChat}>
					{isOpen ? "Close" : "Open"} Chat
				</Button>
				
				<div className="text-sm text-muted-foreground">
					<p>Chat Status: {isOpen ? "Open" : "Closed"}</p>
					<p>Streaming: {isStreaming ? "Yes" : "No"}</p>
					<p>Messages: {messages.length}</p>
					<p>Has Messages: {hasMessages ? "Yes" : "No"}</p>
					{error && <p className="text-destructive">Error: {error}</p>}
				</div>
			</div>
			
			{/* Use the modal component */}
			<ChatModal 
				onSendMessage={sendMessage}
				onStopStreaming={stopStreaming}
			/>
		</div>
	);
};

// Example 5: Integration in layout component
export const LayoutWithChat = ({ children }: { children: React.ReactNode }) => {
	return (
		<div className="min-h-screen bg-background">
			{/* Your layout content */}
			<header>Header content</header>
			
			<main>{children}</main>
			
			<footer>Footer content</footer>
			
			{/* Chat system - available throughout the app */}
			<Chat 
				showTrigger={true}
				triggerPosition="bottom-right"
				triggerSize="md"
			/>
		</div>
	);
};
