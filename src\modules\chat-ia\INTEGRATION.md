# Chat IA Integration Guide

This guide shows how to integrate the Chat IA system into your application.

## Step 1: Add to Layout

The easiest way to add chat to your entire application is to include it in your main layout component.

### Option A: Add to Global Layout

```tsx
// src/app/layout.tsx or your main layout component
import { Chat } from "@/modules/chat-ia";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <ProviderGlobal>
          {children}
          
          {/* Add chat system here */}
          <Chat />
        </ProviderGlobal>
      </body>
    </html>
  );
}
```

### Option B: Add to App Layout Component

```tsx
// src/layout/components/container/app-layout.tsx
import { Chat } from "@/modules/chat-ia";

export const AppLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="min-h-screen bg-background">
      <AppSidebar />
      
      <main className="flex-1">
        {children}
      </main>
      
      {/* Add chat system */}
      <Chat 
        triggerPosition="bottom-right"
        triggerSize="md"
      />
    </div>
  );
};
```

## Step 2: Add to Specific Pages

If you want chat only on specific pages:

```tsx
// src/app/(home)/page.tsx
import { Chat } from "@/modules/chat-ia";

export default function HomePage() {
  return (
    <div>
      <h1>Welcome to the Dashboard</h1>
      
      {/* Page content */}
      
      {/* Chat only on this page */}
      <Chat />
    </div>
  );
}
```

## Step 3: Custom Integration

For more control over the chat behavior:

```tsx
// src/components/custom-chat.tsx
import { ChatModal, ChatTrigger, useChat } from "@/modules/chat-ia";
import { Button } from "@/shared/components/shadcn/button";
import { MessageSquare } from "lucide-react";

export const CustomChat = () => {
  const { 
    sendMessage, 
    stopStreaming, 
    openChat, 
    isOpen, 
    isStreaming,
    hasMessages 
  } = useChat();

  return (
    <>
      {/* Custom trigger in header/navbar */}
      <Button 
        onClick={openChat}
        variant="ghost"
        size="sm"
        className="relative"
      >
        <MessageSquare className="h-4 w-4" />
        {hasMessages && (
          <div className="absolute -top-1 -right-1 h-2 w-2 bg-primary rounded-full" />
        )}
      </Button>
      
      {/* Chat modal */}
      <ChatModal 
        onSendMessage={sendMessage}
        onStopStreaming={stopStreaming}
      />
    </>
  );
};
```

## Step 4: Environment Configuration

Make sure your API endpoint is properly configured:

```env
# .env.local
BACKEND_URL=https://your-api-domain.com
```

The chat will automatically use the configured backend URL with the `/ai/chat/stream` endpoint.

## Step 5: Styling Customization

You can customize the chat appearance:

```tsx
import { Chat } from "@/modules/chat-ia";

export const StyledChat = () => {
  return (
    <Chat 
      triggerPosition="bottom-left"
      triggerSize="lg"
      className="bg-green-500 hover:bg-green-600" // Custom trigger styling
    />
  );
};
```

## Step 6: Advanced Usage

### Programmatic Control

```tsx
import { useChat } from "@/modules/chat-ia";

export const AdvancedChatControl = () => {
  const { sendMessage, openChat, toggleChat } = useChat();

  const handleQuickQuestion = () => {
    openChat();
    sendMessage("What can you help me with?");
  };

  return (
    <div>
      <Button onClick={handleQuickQuestion}>
        Ask AI for Help
      </Button>
      
      <Button onClick={toggleChat}>
        Toggle Chat
      </Button>
    </div>
  );
};
```

### Context-Aware Messages

```tsx
import { useChat } from "@/modules/chat-ia";

export const ContextualChat = ({ currentPage }: { currentPage: string }) => {
  const { sendMessage, openChat } = useChat();

  const askAboutPage = () => {
    openChat();
    sendMessage(`Can you help me with the ${currentPage} page?`);
  };

  return (
    <Button onClick={askAboutPage}>
      Get Help with {currentPage}
    </Button>
  );
};
```

## Troubleshooting

### Common Issues

1. **Chat not appearing**: Make sure the component is inside the Jotai Provider
2. **Styling issues**: Verify Tailwind CSS is properly configured
3. **API errors**: Check the backend URL and endpoint configuration
4. **State not persisting**: Ensure localStorage is available

### Debug Mode

You can monitor chat state for debugging:

```tsx
import { useAtomValue } from "jotai";
import { chatStateAtom } from "@/modules/chat-ia";

export const ChatDebug = () => {
  const chatState = useAtomValue(chatStateAtom);
  
  return (
    <pre className="text-xs">
      {JSON.stringify(chatState, null, 2)}
    </pre>
  );
};
```

## Performance Considerations

- The chat system uses localStorage for session persistence
- Streaming is automatically cleaned up on component unmount
- State is optimized to prevent unnecessary re-renders
- Sessions are automatically cleaned after 7 days

## Security Notes

- All API calls include authentication cookies
- Session IDs are generated client-side for privacy
- No sensitive data is stored in localStorage
- Streaming connections are properly secured
