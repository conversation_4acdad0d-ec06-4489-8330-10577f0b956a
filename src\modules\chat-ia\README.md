# Chat IA Module

A complete real-time streaming chat interface for AI interactions, built with React, TypeScript, and Jotai state management.

## Features

- ✅ **Real-time streaming** - Messages stream in real-time as the AI generates responses
- ✅ **Session management** - Persistent conversation sessions with local storage
- ✅ **Modern UI** - Clean, responsive design following the app's design system
- ✅ **Error handling** - Comprehensive error handling with user feedback
- ✅ **Abort capability** - Stop streaming responses at any time
- ✅ **Typing indicators** - Visual feedback during AI response generation
- ✅ **Floating trigger** - Customizable floating action button
- ✅ **Accessibility** - Full keyboard navigation and screen reader support
- ✅ **TypeScript** - Fully typed for better developer experience

## Quick Start

### 1. Basic Integration

```tsx
import { Chat } from "@/modules/chat-ia";

export const MyApp = () => {
  return (
    <div>
      {/* Your app content */}
      <h1>My Application</h1>
      
      {/* Chat system with floating trigger */}
      <Chat />
    </div>
  );
};
```

### 2. Custom Integration

```tsx
import { ChatModal, ChatTrigger, useChat } from "@/modules/chat-ia";

export const CustomChatApp = () => {
  const { sendMessage, stopStreaming } = useChat();

  return (
    <div>
      {/* Custom trigger */}
      <ChatTrigger position="bottom-left" size="lg" />
      
      {/* Chat modal */}
      <ChatModal 
        onSendMessage={sendMessage}
        onStopStreaming={stopStreaming}
      />
    </div>
  );
};
```

## Components

### `<Chat />`
Complete chat system with trigger and modal.

**Props:**
- `showTrigger?: boolean` - Show floating trigger button (default: true)
- `triggerPosition?: "bottom-right" | "bottom-left" | "top-right" | "top-left"` - Trigger position
- `triggerSize?: "sm" | "md" | "lg"` - Trigger button size
- `className?: string` - Additional CSS classes

### `<ChatModal />`
The main chat interface modal.

**Props:**
- `onSendMessage: (message: string) => void` - Handle sending messages
- `onStopStreaming?: () => void` - Handle stopping stream
- `className?: string` - Additional CSS classes

### `<ChatTrigger />`
Floating action button to open chat.

**Props:**
- `position?: "bottom-right" | "bottom-left" | "top-right" | "top-left"` - Button position
- `size?: "sm" | "md" | "lg"` - Button size
- `className?: string` - Additional CSS classes

## Hooks

### `useChat()`
Main hook for chat functionality.

**Returns:**
```tsx
{
  // State
  chatState: IChatState;
  sessionId: string | null;
  isStreaming: boolean;
  isOpen: boolean;
  hasError: boolean;
  error: string | null;
  messages: IChatMessage[];
  hasMessages: boolean;
  
  // Actions
  sendMessage: (content: string) => Promise<void>;
  stopStreaming: () => void;
  openChat: () => void;
  toggleChat: () => void;
}
```

## API Configuration

The chat system uses the endpoint defined in `CHAT_IA_ENDPOINTS.TO_TALK_STREAM` which points to `/ai/chat/stream`.

**Expected Request Format:**
```json
{
  "message": "string",
  "sessionId": "string",
  "context": {},
  "model": "llama2",
  "temperature": 1
}
```

**Expected Response:**
- Server-Sent Events (SSE) format
- Each chunk should contain JSON with `content` field
- Stream ends with `[DONE]` message

## State Management

The module uses Jotai atoms for state management:

- `chatIsOpenAtom` - Modal open/closed state
- `chatIsStreamingAtom` - Streaming status
- `currentSessionAtom` - Current chat session (persisted)
- `messagesAtom` - Current session messages
- And more...

## Session Management

Sessions are automatically:
- Created when first message is sent
- Persisted to localStorage
- Cleaned up after 7 days
- Limited to 10 most recent sessions

## Error Handling

The system handles various error scenarios:
- Network errors
- API errors
- Streaming interruptions
- Session creation failures

Errors are displayed to users via:
- Toast notifications
- Error messages in chat
- Visual error states

## Styling

The components use the app's design system:
- Tailwind CSS classes
- shadcn/ui components
- Consistent with app theme
- Responsive design

## Examples

See `examples/chat-integration.example.tsx` for comprehensive usage examples.

## Development

### File Structure
```
src/modules/chat-ia/
├── components/          # React components
├── hooks/              # Custom hooks
├── services/           # API and business logic
├── types/              # TypeScript interfaces
├── atoms/              # Jotai state atoms
├── examples/           # Usage examples
└── README.md           # This file
```

### Adding New Features

1. **New Components**: Add to `components/` directory
2. **New Hooks**: Add to `hooks/` directory  
3. **New Services**: Add to `services/` directory
4. **New Types**: Add to `types/` directory
5. **New State**: Add to `atoms/` directory

### Testing

The implementation includes:
- Error boundary handling
- Abort controller cleanup
- Memory leak prevention
- Accessibility features

## Troubleshooting

**Chat not opening:**
- Check if atoms are properly initialized
- Verify Jotai provider is set up

**Streaming not working:**
- Verify API endpoint is correct
- Check network connectivity
- Ensure proper CORS configuration

**Messages not persisting:**
- Check localStorage availability
- Verify session management is working

**Performance issues:**
- Check for memory leaks
- Verify proper cleanup on unmount
- Monitor state updates
