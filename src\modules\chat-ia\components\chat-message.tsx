import { cn } from "@/shared/lib/shadcn/utils";
import { Bo<PERSON>, User } from "lucide-react";
import { memo } from "react";
import type { IChatMessage } from "../types";

interface ChatMessageProps {
	message: IChatMessage;
	isStreaming?: boolean;
}

export const ChatMessage = memo<ChatMessageProps>(({ message, isStreaming = false }) => {
	const isUser = message.role === "user";
	const isAssistant = message.role === "assistant";

	return (
		<div className={cn("flex gap-3 p-4", isUser ? "justify-end" : "justify-start")}>
			{isAssistant && (
				<div className="bg-primary/10 flex h-8 w-8 shrink-0 items-center justify-center rounded-full">
					<Bot className="text-primary h-4 w-4" />
				</div>
			)}
			<div
				className={cn(
					"max-w-[80%] rounded-lg px-3 py-2 text-sm",
					isUser ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground",
					message.isError && "bg-destructive/10 text-destructive border-destructive/20 border",
				)}
			>
				<div className="break-words whitespace-pre-wrap">
					{message.content}
					{isStreaming && <span className="ml-1 inline-block h-4 w-1 animate-pulse bg-current" />}
				</div>
				{!isStreaming && (
					<div className="mt-1 text-xs opacity-70">
						{message.timestamp.toLocaleTimeString([], {
							hour: "2-digit",
							minute: "2-digit",
						})}
					</div>
				)}
			</div>
			{isUser && (
				<div className="bg-primary/10 flex h-8 w-8 shrink-0 items-center justify-center rounded-full">
					<User className="text-primary h-4 w-4" />
				</div>
			)}
		</div>
	);
});

ChatMessage.displayName = "ChatMessage";
