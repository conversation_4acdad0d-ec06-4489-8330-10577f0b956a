import { useChat } from "../hooks";
import { ChatModal } from "./chat-modal";
import { ChatTrigger } from "./chat-trigger";

interface ChatProps {
	showTrigger?: boolean;
	triggerPosition?: "bottom-right" | "bottom-left" | "top-right" | "top-left";
	triggerSize?: "sm" | "md" | "lg";
	className?: string;
}

export const Chat = ({ 
	showTrigger = true, 
	triggerPosition = "bottom-right",
	triggerSize = "md",
	className 
}: ChatProps) => {
	const { sendMessage, stopStreaming } = useChat();

	return (
		<>
			{showTrigger && (
				<ChatTrigger 
					position={triggerPosition}
					size={triggerSize}
					className={className}
				/>
			)}
			
			<ChatModal 
				onSendMessage={sendMessage}
				onStopStreaming={stopStreaming}
			/>
		</>
	);
};
