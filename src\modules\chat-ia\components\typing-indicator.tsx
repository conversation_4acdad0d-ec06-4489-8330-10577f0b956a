import { cn } from "@/shared/lib/shadcn/utils";
import { Bo<PERSON> } from "lucide-react";

interface TypingIndicatorProps {
	className?: string;
}

export const TypingIndicator = ({ className }: TypingIndicatorProps) => {
	return (
		<div className={cn("flex gap-3 p-4", className)}>
			<div className="flex h-8 w-8 shrink-0 items-center justify-center rounded-full bg-primary/10">
				<Bot className="h-4 w-4 text-primary" />
			</div>
			
			<div className="flex items-center gap-1 rounded-lg bg-muted px-3 py-2">
				<div className="flex gap-1">
					<div className="h-2 w-2 animate-bounce rounded-full bg-muted-foreground/60 [animation-delay:-0.3s]" />
					<div className="h-2 w-2 animate-bounce rounded-full bg-muted-foreground/60 [animation-delay:-0.15s]" />
					<div className="h-2 w-2 animate-bounce rounded-full bg-muted-foreground/60" />
				</div>
				<span className="ml-2 text-xs text-muted-foreground">IA está digitando...</span>
			</div>
		</div>
	);
};
