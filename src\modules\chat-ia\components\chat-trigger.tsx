import { Button } from "@/shared/components/shadcn/button";
import { cn } from "@/shared/lib/shadcn/utils";
import { MessageCircle, X } from "lucide-react";
import { useChat } from "../hooks";

interface ChatTriggerProps {
	className?: string;
	position?: "bottom-right" | "bottom-left" | "top-right" | "top-left";
	size?: "sm" | "md" | "lg";
}

const positionClasses = {
	"bottom-right": "bottom-6 right-6",
	"bottom-left": "bottom-6 left-6", 
	"top-right": "top-6 right-6",
	"top-left": "top-6 left-6",
};

const sizeClasses = {
	sm: "h-12 w-12",
	md: "h-14 w-14",
	lg: "h-16 w-16",
};

const iconSizeClasses = {
	sm: "h-5 w-5",
	md: "h-6 w-6", 
	lg: "h-7 w-7",
};

export const ChatTrigger = ({ 
	className, 
	position = "bottom-right", 
	size = "md" 
}: ChatTriggerProps) => {
	const { isOpen, toggleChat, hasMessages, isStreaming } = useChat();

	return (
		<Button
			onClick={toggleChat}
			className={cn(
				"fixed z-50 rounded-full shadow-lg transition-all duration-200 hover:scale-105 active:scale-95",
				positionClasses[position],
				sizeClasses[size],
				isOpen && "rotate-180",
				className
			)}
			size="icon"
			aria-label={isOpen ? "Fechar chat" : "Abrir chat"}
		>
			{isOpen ? (
				<X className={iconSizeClasses[size]} />
			) : (
				<div className="relative">
					<MessageCircle className={iconSizeClasses[size]} />
					
					{/* Notification dot for new activity */}
					{(hasMessages || isStreaming) && (
						<div className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-destructive">
							{isStreaming && (
								<div className="h-full w-full animate-pulse rounded-full bg-destructive" />
							)}
						</div>
					)}
				</div>
			)}
		</Button>
	);
};
